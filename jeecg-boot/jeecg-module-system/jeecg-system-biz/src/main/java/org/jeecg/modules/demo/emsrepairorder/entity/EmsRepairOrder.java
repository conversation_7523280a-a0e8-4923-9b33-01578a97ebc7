package org.jeecg.modules.demo.emsrepairorder.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 维修工单表
 * @Author: jeecg-boot
 * @Date:   2025-07-31
 * @Version: V1.0
 */
@Data
@TableName("ems_repair_order")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="维修工单表")
public class EmsRepairOrder implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "id")
    private java.lang.String id;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
	/**关联设备表*/
	@Excel(name = "关联设备表", width = 15)
    @Schema(description = "关联设备表")
    private java.lang.String equipmentId;
	/**发起人id*/
	@Excel(name = "发起人id", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
	@Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "发起人id")
    private java.lang.String reportId;
	/**负责人id*/
	@Excel(name = "负责人id", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
	@Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "负责人id")
    private java.lang.String principalId;
	/**故障描述*/
	@Excel(name = "故障描述", width = 15)
    @Schema(description = "故障描述")
    private java.lang.String faultDescription;
	/**故障图片*/
	@Excel(name = "故障图片", width = 15)
    @Schema(description = "故障图片")
    private java.lang.String attachment;
	/**故障附件*/
	@Excel(name = "故障附件", width = 15)
    @Schema(description = "故障附件")
    private java.lang.String faultAttachment;
	/**故障标题*/
	@Excel(name = "故障标题", width = 15)
    @Schema(description = "故障标题")
    private java.lang.String faultTitle;
	/**处理图片*/
	@Excel(name = "处理图片", width = 15)
    @Schema(description = "处理图片")
    private java.lang.String handleImages;
	/**处理附件*/
	@Excel(name = "处理附件", width = 15)
    @Schema(description = "处理附件")
    private java.lang.String handleAttachment;
	/**当前审批进度（已完成的步骤数）*/
	@Excel(name = "当前审批进度（已完成的步骤数）", width = 15)
    @Schema(description = "当前审批进度（已完成的步骤数）")
    private java.lang.String approvalProgress;
	/**总审批步骤数（线性审批的总人数）*/
	@Excel(name = "总审批步骤数（线性审批的总人数）", width = 15)
    @Schema(description = "总审批步骤数（线性审批的总人数）")
    private java.lang.String totalApprovalSteps;
}

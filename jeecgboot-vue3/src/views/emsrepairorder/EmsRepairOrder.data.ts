import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '关联设备表',
    align:"center",
    dataIndex: 'equipmentId'
   },
   {
    title: '发起人id',
    align:"center",
    dataIndex: 'reportId_dictText'
   },
   {
    title: '负责人id',
    align:"center",
    dataIndex: 'principalId_dictText'
   },
   {
    title: '故障描述',
    align:"center",
    dataIndex: 'faultDescription'
   },
   {
    title: '故障图片',
    align:"center",
    dataIndex: 'attachment',
    customRender:render.renderImage,
   },
   {
    title: '故障附件',
    align:"center",
    dataIndex: 'faultAttachment',
   },
   {
    title: '故障标题',
    align:"center",
    dataIndex: 'faultTitle'
   },
   {
    title: '处理图片',
    align:"center",
    dataIndex: 'handleImages',
    customRender:render.renderImage,
   },
   {
    title: '处理附件',
    align:"center",
    dataIndex: 'handleAttachment',
   },
   {
    title: '维修部门',
    align:"center",
    dataIndex: 'repairDeptName'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '维修部门',
    field: 'repairDeptName',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '故障标题',
    field: 'faultTitle',
    component: 'Input',
    colProps: { span: 6 },
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '关联设备表',
    field: 'equipmentId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入关联设备表!'},
          ];
     },
  },
  {
    label: '发起人id',
    field: 'reportId',
    component: 'JSelectUser',
    componentProps:{
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入发起人id!'},
          ];
     },
  },
  {
    label: '负责人id',
    field: 'principalId',
    component: 'JSelectUser',
    componentProps:{
    },
  },
  {
    label: '故障描述',
    field: 'faultDescription',
    component: 'InputTextArea',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入故障描述!'},
          ];
     },
  },
  {
    label: '故障图片',
    field: 'attachment',
     component: 'JImageUpload',
     componentProps:{
        fileMax: 0
      },
  },
  {
    label: '故障附件',
    field: 'faultAttachment',
    component: 'JUpload',
    componentProps:{
     },
  },
  {
    label: '故障标题',
    field: 'faultTitle',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入故障标题!'},
          ];
     },
  },
  {
    label: '处理图片',
    field: 'handleImages',
     component: 'JImageUpload',
     componentProps:{
        fileMax: 0
      },
  },
  {
    label: '处理附件',
    field: 'handleAttachment',
    component: 'JUpload',
    componentProps:{
     },
  },
  {
    label: '维修部门角色',
    field: 'repairDeptRole',
    component: 'JDeptRoleSelector',
    componentProps:{
      placeholder: '请选择维修部门和角色'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请选择维修部门和角色!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
	// 隐藏字段：维修部门ID
	{
	  label: '',
	  field: 'repairDeptId',
	  component: 'Input',
	  show: false
	},
	// 隐藏字段：维修部门名称
	{
	  label: '',
	  field: 'repairDeptName',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  equipmentId: {title: '关联设备表',order: 0,view: 'text', type: 'string',},
  reportId: {title: '发起人id',order: 1,view: 'sel_user', type: 'string',},
  principalId: {title: '负责人id',order: 2,view: 'sel_user', type: 'string',},
  faultDescription: {title: '故障描述',order: 3,view: 'textarea', type: 'string',},
  attachment: {title: '故障图片',order: 4,view: 'image', type: 'string',},
  faultAttachment: {title: '故障附件',order: 5,view: 'file', type: 'string',},
  faultTitle: {title: '故障标题',order: 6,view: 'text', type: 'string',},
  handleImages: {title: '处理图片',order: 7,view: 'image', type: 'string',},
  handleAttachment: {title: '处理附件',order: 8,view: 'file', type: 'string',},
  repairDeptName: {title: '维修部门',order: 9,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}

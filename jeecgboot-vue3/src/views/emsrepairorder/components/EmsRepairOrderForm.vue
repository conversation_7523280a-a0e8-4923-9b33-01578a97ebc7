<template>
    <div style="min-height: 400px">
        <BasicForm @register="registerForm"></BasicForm>
        <div style="width: 100%;text-align: center" v-if="!formDisabled">
            <a-button @click="submitForm" pre-icon="ant-design:check" type="primary">提 交</a-button>
        </div>
    </div>
</template>

<script lang="ts">
    import {BasicForm, useForm} from '/@/components/Form/index';
    import {computed, defineComponent} from 'vue';
    import {defHttp} from '/@/utils/http/axios';
    import { propTypes } from '/@/utils/propTypes';
    import {getBpmFormSchema} from '../EmsRepairOrder.data';
    import {saveOrUpdate} from '../EmsRepairOrder.api';

    export default defineComponent({
        name: "EmsRepairOrderForm",
        components:{
            BasicForm
        },
        props:{
            formData: propTypes.object.def({}),
            formBpm: propTypes.bool.def(true),
        },
        setup(props){
            const [registerForm, { setFieldsValue, setProps, getFieldsValue }] = useForm({
                labelWidth: 150,
                schemas: getBpmFormSchema(props.formData),
                showActionButtonGroup: false,
                baseColProps: {span: 24}
            });

            const formDisabled = computed(()=>{
                if(props.formData.disabled === false){
                    return false;
                }
                return true;
            });

            let formData = {};
            const queryByIdUrl = '/emsrepairorder/emsRepairOrder/queryById';
            async function initFormData(){
                let params = {id: props.formData.dataId};
                const data = await defHttp.get({url: queryByIdUrl, params});
                formData = {...data}

                // 处理部门角色数据回显
                if (data.repairDeptRole && typeof data.repairDeptRole === 'string') {
                    try {
                        formData.repairDeptRole = JSON.parse(data.repairDeptRole);
                    } catch (error) {
                        console.warn('解析部门角色数据失败:', error);
                        formData.repairDeptRole = null;
                    }
                }

                //设置表单的值
                await setFieldsValue(formData);
                //默认是禁用
                await setProps({disabled: formDisabled.value})
            }

            async function submitForm() {
                let data = getFieldsValue();
                let params = Object.assign({}, formData, data);

                // 处理部门角色数据
                if (params.repairDeptRole && typeof params.repairDeptRole === 'object') {
                    // 提取部门信息到单独字段
                    params.repairDeptId = params.repairDeptRole.departId;
                    params.repairDeptName = params.repairDeptRole.departName;
                    // 将完整数据转为JSON字符串存储
                    params.repairDeptRole = JSON.stringify(params.repairDeptRole);
                }

                console.log('表单数据', params)
                await saveOrUpdate(params, true)
            }

            initFormData();

            return {
                registerForm,
                formDisabled,
                submitForm,
            }
        }
    });
</script>
